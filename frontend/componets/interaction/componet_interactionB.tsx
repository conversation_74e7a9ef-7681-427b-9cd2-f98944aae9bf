import React from 'react';
import { useButtonStore } from '../../Store/store';
import ComponetB1 from '../componet/componetB1';
import ComponetB2 from '../componet/componetB2';
import ComponetButton from '../componet/componetButton';

const ComponetInteractionB: React.FC = () => {
  const { modeButtonActive, businessButtonActive } = useButtonStore();

  return (
    <div style={{
      position: 'relative',
      height: '95vh',
      width: '20vw',
      marginLeft: '1vw',
    }}>
      {/* 按键容器始终可见，绝对定位在顶部 */}
      <ComponetButton />
      
      {/* 根据按键状态显示对应的容器 */}
      {modeButtonActive && (
        <div style={{
          height: '100%',
          width: '100%',
        }}>
          <ComponetB1 />
        </div>
      )}
      
      {businessButtonActive && (
        <div style={{
          height: '100%',
          width: '100%',
        }}>
          <ComponetB2 />
        </div>
      )}
    </div>
  );
};

export default ComponetInteractionB;
