import React from 'react';
import ComponetA1 from './componetA1';
import ComponetInteractionB from '../interaction/componet_interactionB';

const MainContainer: React.FC = () => {
  return (
    <div style={{
      height: '100vh',
      width: '100vw',
      backgroundColor: '#242424',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      scrollbarWidth: 'none',
      msOverflowStyle: 'none',
    }}>
      <style jsx>{`
        div::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <ComponetA1 />
      <ComponetInteractionB />
    </div>
  );
};

export default MainContainer;
