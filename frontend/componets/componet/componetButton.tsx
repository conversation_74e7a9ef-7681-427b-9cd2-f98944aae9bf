import React from 'react';
import { useButtonStore } from '../../Store/store';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';

const ComponetButton: React.FC = () => {
  const { 
    modeButtonActive, 
    businessButtonActive, 
    toggleModeButton, 
    toggleBusinessButton 
  } = useButtonStore();

  return (
    <div style={{
      position: 'absolute',
      top: '0',
      left: '0',
      height: '3vh',
      width: '20vw',
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      overflow: 'hidden',
      backgroundColor: 'transparent',
    }}>
      {/* 模式按键 */}
      <SecondaryButton
        text="模式"
        style={{
          width: '50%',
          height: '100%',
          fontSize: 'clamp(8px, 1.5vw, 16px)', // 自适应字体大小
        }}
        isActive={modeButtonActive}
        disabled={modeButtonActive}
        onClick={toggleModeButton}
        mode="toggle"
      />

      {/* 业务按键 */}
      <SecondaryButton
        text="业务"
        style={{
          width: '50%',
          height: '100%',
          fontSize: 'clamp(8px, 1.5vw, 16px)', // 自适应字体大小
        }}
        isActive={businessButtonActive}
        disabled={businessButtonActive}
        onClick={toggleBusinessButton}
        mode="toggle"
      />
    </div>
  );
};

export default ComponetButton;
