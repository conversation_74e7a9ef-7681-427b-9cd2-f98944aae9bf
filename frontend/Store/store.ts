import { create } from 'zustand';

// 按键状态接口
interface ButtonState {
  modeButtonActive: boolean;
  businessButtonActive: boolean;
  setModeButtonActive: (active: boolean) => void;
  setBusinessButtonActive: (active: boolean) => void;
  toggleModeButton: () => void;
  toggleBusinessButton: () => void;
}

// Zustand 状态管理
export const useButtonStore = create<ButtonState>((set) => ({
  // 默认状态：模式按键激活，业务按键未激活
  modeButtonActive: true,
  businessButtonActive: false,

  // 设置模式按键状态
  setModeButtonActive: (active: boolean) => set((state) => ({
    modeButtonActive: active,
    businessButtonActive: active ? false : state.businessButtonActive
  })),

  // 设置业务按键状态
  setBusinessButtonActive: (active: boolean) => set((state) => ({
    businessButtonActive: active,
    modeButtonActive: active ? false : state.modeButtonActive
  })),

  // 切换模式按键（点击时激活模式，禁用业务）
  toggleModeButton: () => set(() => ({
    modeButtonActive: true,
    businessButtonActive: false
  })),

  // 切换业务按键（点击时激活业务，禁用模式）
  toggleBusinessButton: () => set(() => ({
    modeButtonActive: false,
    businessButtonActive: true
  }))
}));
