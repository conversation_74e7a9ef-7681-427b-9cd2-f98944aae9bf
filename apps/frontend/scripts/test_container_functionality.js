// 一二级容器功能测试脚本
// 测试日期: 2025-07-31

console.log('=== 一二级容器功能测试开始 ===');

// 测试1: 检查页面是否正常加载
function testPageLoad() {
  console.log('测试1: 页面加载检查');
  
  const mainContainer = document.querySelector('[style*="background-color: rgb(36, 36, 36)"]');
  if (mainContainer) {
    console.log('✅ 主容器加载成功');
    console.log('✅ 背景色正确: #242424');
  } else {
    console.log('❌ 主容器未找到');
    return false;
  }
  
  return true;
}

// 测试2: 检查按键是否存在
function testButtonsExist() {
  console.log('\n测试2: 按键存在性检查');
  
  const buttons = document.querySelectorAll('button');
  const modeButton = Array.from(buttons).find(btn => btn.textContent.includes('模式'));
  const businessButton = Array.from(buttons).find(btn => btn.textContent.includes('业务'));
  
  if (modeButton) {
    console.log('✅ 模式按键存在');
  } else {
    console.log('❌ 模式按键未找到');
    return false;
  }
  
  if (businessButton) {
    console.log('✅ 业务按键存在');
  } else {
    console.log('❌ 业务按键未找到');
    return false;
  }
  
  return true;
}

// 测试3: 检查容器切换功能
function testContainerSwitching() {
  console.log('\n测试3: 容器切换功能检查');
  
  const buttons = document.querySelectorAll('button');
  const modeButton = Array.from(buttons).find(btn => btn.textContent.includes('模式'));
  const businessButton = Array.from(buttons).find(btn => btn.textContent.includes('业务'));
  
  if (!modeButton || !businessButton) {
    console.log('❌ 按键未找到，无法进行切换测试');
    return false;
  }
  
  // 模拟点击业务按键
  console.log('点击业务按键...');
  businessButton.click();
  
  setTimeout(() => {
    console.log('检查容器切换状态...');
    // 这里可以添加更多的状态检查逻辑
    console.log('✅ 按键点击功能正常');
  }, 100);
  
  return true;
}

// 执行所有测试
function runAllTests() {
  const test1 = testPageLoad();
  const test2 = testButtonsExist();
  const test3 = testContainerSwitching();
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`页面加载: ${test1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`按键存在: ${test2 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`切换功能: ${test3 ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = test1 && test2 && test3;
  console.log(`\n总体结果: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
  
  return allPassed;
}

// 等待页面加载完成后执行测试
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runAllTests);
} else {
  runAllTests();
}

console.log('=== 测试脚本加载完成 ===');
console.log('请在浏览器控制台中查看测试结果');
