// TypeScript类型修复验证测试
// 测试日期: 2025-07-31

import { secondaryButtonStyles, getSecondaryButtonStyle } from '../../../frontend/ButtonCodes/secondary/style/style_secondary';

// 测试1: 验证样式对象类型正确
function testStyleTypes() {
  console.log('测试1: 验证样式对象类型');
  
  // 检查默认样式是否符合React.CSSProperties类型
  const defaultStyle = secondaryButtonStyles.default;
  
  // 这些属性应该都能正常访问，不会有TypeScript错误
  const width = defaultStyle.width;
  const height = defaultStyle.height;
  const textAlign = defaultStyle.textAlign; // 这个之前有错误，现在应该正常
  const backgroundColor = defaultStyle.backgroundColor;
  
  console.log('✅ 默认样式类型检查通过');
  console.log(`   width: ${width}`);
  console.log(`   height: ${height}`);
  console.log(`   textAlign: ${textAlign}`);
  console.log(`   backgroundColor: ${backgroundColor}`);
  
  return true;
}

// 测试2: 验证getSecondaryButtonStyle函数返回类型
function testStyleFunction() {
  console.log('\n测试2: 验证样式函数返回类型');
  
  // 测试不同状态下的样式获取
  const toggleActiveStyle = getSecondaryButtonStyle('toggle', true, false, false);
  const toggleInactiveStyle = getSecondaryButtonStyle('toggle', false, false, false);
  const instantStyle = getSecondaryButtonStyle('instant', false, true, false);
  
  // 验证返回的样式对象符合React.CSSProperties类型
  console.log('✅ 样式函数返回类型检查通过');
  console.log(`   toggle active backgroundColor: ${toggleActiveStyle.backgroundColor}`);
  console.log(`   toggle inactive backgroundColor: ${toggleInactiveStyle.backgroundColor}`);
  console.log(`   instant hover backgroundColor: ${instantStyle.backgroundColor}`);
  
  return true;
}

// 测试3: 验证textAlign属性值正确
function testTextAlignProperty() {
  console.log('\n测试3: 验证textAlign属性值');
  
  const style = secondaryButtonStyles.default;
  const textAlign = style.textAlign;
  
  // textAlign应该是'center'
  if (textAlign === 'center') {
    console.log('✅ textAlign属性值正确: center');
    return true;
  } else {
    console.log(`❌ textAlign属性值错误: ${textAlign}`);
    return false;
  }
}

// 执行所有测试
function runTypeScriptFixTests() {
  console.log('=== TypeScript类型修复验证测试开始 ===\n');
  
  const test1 = testStyleTypes();
  const test2 = testStyleFunction();
  const test3 = testTextAlignProperty();
  
  console.log('\n=== 测试结果汇总 ===');
  console.log(`样式类型检查: ${test1 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`函数返回类型: ${test2 ? '✅ 通过' : '❌ 失败'}`);
  console.log(`textAlign属性: ${test3 ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = test1 && test2 && test3;
  console.log(`\n总体结果: ${allPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
  
  return allPassed;
}

// 导出测试函数
export { runTypeScriptFixTests };

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTypeScriptFixTests();
}
